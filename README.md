# Next.js 13 Clarify - AI-Powered Research & Analysis Platform

> **AI System Note**: This is a Next.js 13+ application with TypeScript, featuring interactive tree-based research tools, AI-powered content generation, and comprehensive testing infrastructure. The codebase follows modern React patterns with performance optimizations and is designed for collaborative AI-human development.

## 🏗️ Architecture Overview

### Core Technologies

- **Framework**: Next.js 15.3.3 with App Router
- **Database**: Prisma ORM with PostgreSQL
- **Authentication**: NextAuth.js v4
- **State Management**: Zustand for global state
- **UI**: Radix UI components with Tailwind CSS
- **AI Integration**: Vercel AI SDK with Azure OpenAI
- **Visualization**: React Flow for interactive diagrams
- **Testing**: Jest + <PERSON><PERSON> with comprehensive coverage

### Key Features

1. **DragTree System**: Interactive research tree builder with AI assistance
2. **Issue Tree Analysis**: Hierarchical problem decomposition
3. **Real-time AI Streaming**: Live content generation with throttled updates
4. **Research Integration**: Multi-source search with AI summarization
5. **Collaborative Notebooks**: Rich text editing with Tiptap
6. **Performance Monitoring**: Built-in performance tracking and optimization

## 🚀 Quick Start

### Prerequisites

```bash
# Node.js 18.17+ required
node --version

# Database setup (PostgreSQL recommended)
# Set up your database connection in .env.local
```

### Installation & Development

```bash
# Install dependencies
npm install

# Set up database
npx prisma generate
npx prisma migrate dev

# Start development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

### Environment Variables

Create `.env.local` with:

```bash
# Database
DATABASE_URL="postgresql://..."

# Authentication
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# AI Services
AZURE_OPENAI_API_KEY="your-azure-key"
AZURE_OPENAI_ENDPOINT="your-azure-endpoint"

# Optional: Analytics & Monitoring
VERCEL_ANALYTICS="true"
MIXPANEL_TOKEN="your-token"
```

## 📁 Project Structure

```
next13-clarify/
├── app/                          # Next.js App Router
│   ├── (conv)/                   # Conversation & Research Routes
│   │   ├── dragTree/            # Main DragTree Application
│   │   │   └── [dragTreeId]/    # Dynamic tree instances
│   │   │       ├── components/  # Tree-specific components
│   │   │       ├── hooks/       # Custom hooks for tree logic
│   │   │       └── utils/       # Tree utilities & algorithms
│   │   ├── conversations/       # Legacy conversation system
│   │   └── screening/           # AI-powered question refinement
│   ├── api/                     # API Routes
│   │   ├── dragtree/           # DragTree API endpoints
│   │   ├── screening/          # Question analysis APIs
│   │   └── auth/               # Authentication APIs
│   ├── components/             # Shared UI components
│   ├── stores/                 # Zustand state management
│   │   └── dragtree_store/     # Main tree state with utilities
│   └── server-actions/         # Server actions for data mutations
├── lib/                        # Shared utilities
│   ├── environment.ts          # Environment detection utilities
│   └── utils.ts               # Common helper functions
├── __tests__/                  # Unit tests
├── e2e/                       # End-to-end tests
└── prisma/                    # Database schema & migrations
```

## 🧪 Testing Infrastructure

### Available Scripts

```bash
# Unit Tests (Jest + React Testing Library)
npm run test              # Run all unit tests
npm run test:watch        # Watch mode for development
npm run test:coverage     # Generate coverage reports

# E2E Tests (Playwright)
npm run test:e2e          # Headless cross-browser testing
npm run test:e2e:ui       # Interactive test UI
npm run test:e2e:debug    # Step-by-step debugging

# Combined Testing
npm run test:all          # Run both unit and e2e tests
```

### Code Quality

- **Pre-commit Hooks**: Automatic linting, formatting, and testing
- **ESLint + Prettier**: Consistent code style
- **TypeScript**: Full type safety
- **Coverage Reports**: Generated in `coverage/` directory

## 🎯 Core Components & Architecture

### DragTree System (`app/(conv)/dragTree/`)

The main research tool with hierarchical tree structures:

**Key Components:**

- `Client.tsx` - Main orchestrator with AI streaming
- `HierarchicalOutline/` - Tree editor with drag-drop
- `VisualFlowDiagram/` - Interactive flow visualization
- `ResizableLayout.tsx` - Split-pane interface

**Performance Optimizations:**

- React.memo on expensive components
- Vercel AI SDK throttling (120ms) for streaming
- Optimized useEffect dependencies
- Debounced database synchronization (1.5s)

### State Management (`app/stores/`)

Zustand-based stores with utilities:

```typescript
// Example usage
const { frontendTreeStructure, addNode, loadFromDatabase } = useDragTreeStore()

// Optimized for performance
const nodeMap = buildNodeMap(treeStructure) // O(1) lookups
const interestedNodes = collectInterestedNodes(tree)
```

### AI Integration

- **Streaming**: Real-time content generation with throttling
- **Multi-model Support**: Azure OpenAI integration
- **Research Tools**: Web search integration with Brave Search
- **Content Management**: Versioned research results per node

## ⚠️ AI SDK v5 Tool Calling - Critical Troubleshooting Guide

> **IMPORTANT**: This section documents critical issues with Vercel AI SDK v5 tool calling that can waste hours of debugging time. Read this before implementing tool calling features.

### The Problem: Tool Calls Execute But No Final Response

**Symptoms:**

- AI calls tools successfully and gets results
- Tool execution completes with correct output
- Stream ends abruptly with `data: [DONE]` after tool execution
- No final AI response incorporating tool results
- User sees tool execution but no explanation/summary

**Root Causes & Solutions:**

#### 1. **Message Format Issue (CRITICAL)**

**Problem**: AI SDK v5 changed message format from v4, breaking tool calling completion.

```typescript
// ❌ OLD FORMAT (v4) - CAUSES TOOL CALLING TO FAIL
{
  "id": "1",
  "role": "user",
  "content": "Calculate 123 * 456"
}

// ✅ NEW FORMAT (v5) - REQUIRED FOR TOOL CALLING
{
  "id": "1",
  "role": "user",
  "parts": [
    {
      "type": "text",
      "text": "Calculate 123 * 456"
    }
  ]
}
```

**Solution**: Always use the `parts` array format for AI SDK v5.

#### 2. **Multi-Step Configuration Issue**

**Problem**: Using wrong parameter for server-side multi-step tool calling.

```typescript
// ❌ WRONG - maxSteps is for client-side useChat
const result = streamText({
  model: openai('gpt-4o'),
  messages: convertToModelMessages(messages),
  tools,
  maxSteps: 5, // This doesn't work in streamText!
})

// ✅ CORRECT - Use stopWhen for server-side
const result = streamText({
  model: openai('gpt-4o'),
  messages: convertToModelMessages(messages),
  tools,
  stopWhen: stepCountIs(5), // This enables multi-step tool calling
})
```

#### 3. **Missing convertToModelMessages**

**Problem**: Not converting UIMessage format to ModelMessage format.

```typescript
// ❌ WRONG - Direct message passing
const result = streamText({
  model: openai('gpt-4o'),
  messages: messages, // Raw UIMessage format
  tools,
})

// ✅ CORRECT - Convert messages first
const result = streamText({
  model: openai('gpt-4o'),
  messages: convertToModelMessages(messages), // Convert to ModelMessage
  tools,
})
```

### Complete Working Example

```typescript
// app/api/chat-demo/route.ts
import {
  streamText,
  tool,
  UIMessage,
  convertToModelMessages,
  stepCountIs,
} from 'ai'
import { openai } from '@ai-sdk/openai'
import { z } from 'zod'

export async function POST(req: Request) {
  try {
    const { messages }: { messages: UIMessage[] } = await req.json()

    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 })
    }

    const result = streamText({
      model: openai('gpt-4o'),
      messages: convertToModelMessages(messages), // CRITICAL: Convert format
      tools: {
        calculateMath: tool({
          description: 'Perform mathematical calculations',
          parameters: z.object({
            expression: z
              .string()
              .describe('Mathematical expression to evaluate'),
          }),
          execute: async ({ expression }) => {
            // Tool implementation
            return { result: eval(expression), expression }
          },
        }),
      },
      toolChoice: 'auto',
      stopWhen: stepCountIs(10), // CRITICAL: Enable multi-step
      system: `You are a helpful AI assistant with access to tools.

IMPORTANT: After using any tools, you MUST provide a final response that
summarizes the results and answers the user's question. Do not end the
conversation after just calling tools.`,
    })

    return result.toUIMessageStreamResponse()
  } catch (error) {
    console.error('Chat API error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}
```

### Client-Side Message Format

When sending messages from the frontend, use the v5 format:

```typescript
// ✅ CORRECT - Frontend message format
const sendMessage = () => {
  fetch('/api/chat-demo', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      messages: [
        {
          id: generateId(),
          role: 'user',
          parts: [
            {
              type: 'text',
              text: userInput,
            },
          ],
        },
      ],
    }),
  })
}
```

### Debugging Tips

1. **Check Message Format**: Log incoming messages to verify format
2. **Verify Tool Execution**: Ensure tools have `execute` functions
3. **Monitor Stream Events**: Look for `start-step`, `finish-step` patterns
4. **Test with Simple Tools**: Start with basic tools before complex ones

### Error Messages to Watch For

- `Cannot read properties of undefined (reading 'filter')` → Message format issue
- Tool calls but no final response → Missing `stopWhen` configuration
- `TypeError` in `convertToModelMessages` → Wrong message structure

### References

- [AI SDK v5 Troubleshooting](https://ai-sdk.dev/docs/troubleshooting/use-chat-tools-no-response)
- [Multi-Step Tool Calls](https://ai-sdk.dev/docs/ai-sdk-core/tools-and-tool-calling)
- [Message Format Migration](https://ai-sdk.dev/docs/migration-guides/migration-guide-5-0)

## 🧠 Azure OpenAI Reasoning Models Integration

### Overview

This project includes support for Azure OpenAI's reasoning models (o-series) that provide step-by-step reasoning capabilities. While Azure's full Responses API with detailed reasoning summaries requires special access, we've implemented a working solution that leverages reasoning models through the standard AI SDK.

### Implementation

**API Endpoint**: `/api/chat-reasoning`
**Demo Page**: `/dragTree/reasoning-demo`

### Key Features

1. **Model Fallback Strategy**: Tries reasoning models first (o3-mini, o4-mini, o1-mini), then falls back to regular models
2. **Reasoning Indication**: Shows when reasoning models are used vs regular models
3. **Step-by-Step Responses**: Reasoning models naturally provide more structured, step-by-step responses
4. **Error Handling**: Graceful fallback if reasoning models are unavailable

### Code Example

```typescript
// API Route: app/api/chat-reasoning/route.ts
const reasoningModels = ['o3-mini', 'o4-mini', 'o1-mini']
const fallbackModels = ['gpt-4.1', 'gpt-4.1-mini']

// Try reasoning models first
for (const model of reasoningModels) {
  try {
    const result = await generateText({
      model: azure(model),
      messages: convertToModelMessages(uiMessages),
      temperature: 0.7,
      maxOutputTokens: 2000,
    })
    // Success with reasoning model
    hasReasoning = true
    break
  } catch (error) {
    // Try next model
    continue
  }
}
```

### Frontend Integration

```typescript
// Frontend: app/(conv)/dragTree/reasoning-demo/page.tsx
const response = await fetch('/api/chat-reasoning', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [
      {
        id: '1',
        role: 'user',
        parts: [{ type: 'text', text: userInput }],
      },
    ],
  }),
})

const data = await response.json()
// data.reasoning.hasReasoning indicates if reasoning model was used
// data.reasoning.summary contains reasoning information
```

### Reasoning vs Regular Models

**Reasoning Models (o3-mini, o4-mini, o1-mini)**:

- Provide more structured, step-by-step responses
- Better at complex problem-solving and mathematical reasoning
- Naturally break down problems into logical steps
- May have higher latency due to reasoning process

**Regular Models (gpt-4.1, gpt-4.1-mini)**:

- Faster response times
- Good for general conversation and simple tasks
- Used as fallback when reasoning models are unavailable

### Testing

```bash
# Test the reasoning API directly
curl -X POST http://localhost:3001/api/chat-reasoning \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"id":"1","role":"user","parts":[{"type":"text","text":"Solve this step by step: If I have 3 boxes with 4 apples each, and I eat 2 apples from the first box, how many apples do I have left in total?"}]}]}'

# Expected response includes:
# - model: "o3-mini" (or fallback model)
# - reasoning.hasReasoning: true/false
# - message.content: step-by-step solution
```

### Future Enhancements

To get full reasoning summaries (the actual chain-of-thought), you would need:

1. **Azure Responses API Access**: Request access through [aka.ms/oai/o3access](https://aka.ms/oai/o3access)
2. **Responses API Implementation**: Use Azure's `/openai/v1/responses` endpoint instead of Chat Completions
3. **Reasoning Summary Parsing**: Extract `reasoning.summary` from the Responses API output

The current implementation provides a solid foundation and can be easily upgraded when full reasoning access is available.

## 🔧 Development Guidelines

### Performance Best Practices

1. **useEffect Dependencies**: Use specific dependencies (`.id`, `.size`) instead of full objects
2. **Memoization**: Implement React.memo for expensive components
3. **Console Logging**: Use `isDevelopmentOrCi()` for conditional logging
4. **Database Operations**: Leverage debounced sync utilities
5. **State Updates**: Prefer atomic operations with proper error handling

### Component Patterns

```typescript
// Preferred: Memoized component with optimized dependencies
const ExpensiveComponent = React.memo(({ data }) => {
  const processedData = useMemo(() => {
    return processData(data)
  }, [data.id, data.lastModified]) // Specific dependencies

  return <div>{/* component JSX */}</div>
})

// State management with Zustand
const useStore = create((set, get) => ({
  data: null,
  updateData: (newData) => set({ data: newData }),
  // Async actions with error handling
  loadData: async () => {
    try {
      const result = await fetchData()
      if (result.success) {
        set({ data: result.data })
      }
    } catch (error) {
      console.error('Failed to load data:', error)
    }
  }
}))
```

### Testing Patterns

```typescript
// Unit test example
describe('DragTree Component', () => {
  it('renders tree structure correctly', () => {
    render(<DragTree data={mockTreeData} />)
    expect(screen.getByTestId('tree-root')).toBeInTheDocument()
  })
})

// E2E test example
test('user can create and edit research tree', async ({ page }) => {
  await page.goto('/dragTree/new')
  await page.click('[data-testid="add-category"]')
  await expect(page.locator('.tree-node')).toBeVisible()
})
```

## 🚀 Deployment

### Production Build

```bash
# Build optimization
npm run build

# Vercel deployment (with Prisma)
npm run vercel-build
```

### Environment-Specific Features

- **Development**: Debug logging, performance monitoring, hot reload
- **Production**: Optimized bundles, CDN assets, rate limiting
- **CI/CD**: Automated testing, lint checks, security scans

## 📑 Tab System Architecture

The application features a comprehensive browser-like tab system for enhanced user experience when working with research content. This system allows users to open research content in dedicated tabs while maintaining the main React Flow view.

### Tab System Components

**Core Components:**

- `TabContainer.tsx` - Main tab container with browser-like scrolling
- `TabButton.tsx` - Individual tab buttons with close functionality
- `useTabStore.ts` - Zustand store for tab state management
- `ResearchContainer.tsx` - Reusable research content wrapper

**Key Features:**

- Browser-like tab navigation with scroll controls
- Auto-scroll to active tab when switching
- Content synchronization between main view and tabs
- Debounced saving with status indicators
- DRY architecture for reusable research components

### Tab System Implementation

```typescript
// Tab interface
interface Tab {
  id: string
  title: string // Truncated display title
  fullTitle: string // Full path with categories
  type: 'main' | 'research'
  nodeId?: string
  contentId?: string
  isClosable: boolean
}

// Usage in components
const { tabs, activeTabId, addTab, removeTab } = useTabStore()

// Opening research in new tab
const openInTab = () => {
  addTab({
    type: 'research',
    nodeId: 'node-123',
    contentId: 'content-456',
    title: 'Research Question',
    fullTitle: 'Category > Subcategory > Research Question',
  })
}
```

### Tab Layout & Constraints

**Critical Layout Fixes Applied:**

- Added `max-w-full` constraints to prevent screen width expansion
- Implemented proper CSS containment hierarchy
- Browser-like tab scrolling with chevron controls
- Fixed container overflow issues in parent layouts

**Container Hierarchy:**

1. `ConvLayoutClient` - Root layout with width constraints
2. `MainContent` - Content area with overflow hidden
3. `ResizableLayout` - Split-pane interface with max-width
4. `TabContainer` - Tab system with scroll controls

### Research Components (DRY Architecture)

**Reusable Components:**

- `ResearchHeader.tsx` - Header with category navigation
- `ResearchEditor.tsx` - Multi-variant content editor
- `ResearchContainer.tsx` - Complete research solution

**Editor Variants:**

- `compact` - Small height for quick research
- `default` - Standard editing height
- `fullscreen` - Full height for tab editing

**Layout Variants:**

- `inline` - Embedded in tree view
- `tab` - Full-screen tab layout
- `modal` - Popup overlay
- `compact` - Space-efficient display

```typescript
// Example usage
<ResearchContainer
  nodeId="node-123"
  contentId="content-456"
  variant="tab"
  showHeader={true}
  showNavigation={true}
  showSearchResults={true}
  showStatusFooter={true}
/>
```

### Performance Optimizations

**Tab System Performance:**

- Conditional tab rendering (only active tab rendered)
- Debounced scroll state checks
- Optimized tab button memoization
- Efficient content synchronization

**Content Synchronization:**

- Shared state between main view and tabs
- Real-time updates across all views
- Consistent saving status indicators
- Throttled auto-save functionality

### Layout Constraints Solution

**Problem Solved:** Tab overflow was causing entire screen to expand beyond viewport width.

**Solution Applied:**

1. Added `max-w-full` to all container components
2. Implemented `overflow-hidden` at layout level
3. Created browser-like tab scrolling system
4. Fixed CSS layout hierarchy constraints

**Files Modified:**

- `ConvLayoutClient.tsx` - Added width constraints to main content area
- `MainContent.tsx` - Applied max-width to root container
- `ResizableLayout.tsx` - Added width containment
- `TabContainer.tsx` - Implemented scroll controls and width limits

### Tab System Usage Guidelines

**For AI Systems:**

1. **Tab Creation**: Use `addTab()` from `useTabStore` to create new tabs
2. **Content Rendering**: Ensure research content uses `ResearchContainer` for consistency
3. **Layout Constraints**: Always include `max-w-full` in container components
4. **Performance**: Leverage conditional rendering and memoization patterns
5. **Synchronization**: Use shared state for content consistency

**Best Practices:**

- Keep tab titles concise but informative
- Use full category path in `fullTitle` for context
- Implement proper error boundaries for tab content
- Test tab overflow scenarios with many tabs
- Maintain accessibility standards for keyboard navigation

## 🤝 AI Collaboration Guidelines

### For AI Systems Working on This Codebase:

1. **Safety First**: Always use existing utilities (`isDevelopmentOrCi()`, `debouncedDbSync()`)
2. **Performance**: Prefer optimizations over breaking changes
3. **Testing**: Run `npm run test:all` before major changes
4. **State Management**: Follow Zustand patterns in existing stores
5. **API Design**: Use server actions for mutations, API routes for external integrations
6. **TypeScript**: Maintain strict type safety throughout
7. **Tab System**: Use established tab patterns and always include width constraints
8. **🚨 AI SDK Tool Calling**: **CRITICAL** - Read the troubleshooting guide above before implementing tool calling. Use v5 message format with `parts` array and `stopWhen: stepCountIs(n)` for multi-step tool calling.

### Common Tasks:

- **Adding Features**: Start with `/dragTree/[dragTreeId]/components/`
- **Performance Issues**: Check `usePerformanceMonitor` usage
- **State Updates**: Use store utilities in `/stores/dragtree_store/utils/`
- **API Changes**: Update corresponding types in `/types/`

### Code Review Checklist:

- [ ] TypeScript errors resolved
- [ ] Tests passing (`npm run test:all`)
- [ ] Performance optimizations considered
- [ ] Error handling implemented
- [ ] Loading states managed
- [ ] Accessibility maintained
- [ ] **AI SDK Tool Calling**: If implementing tool calling, verified v5 format and multi-step configuration

## 📚 Additional Resources

- **API Documentation**: See `/api/` route handlers
- **Component Library**: Check `/components/` for reusable UI
- **Database Schema**: Review `prisma/schema.prisma`
- **Testing Guide**: Detailed info in `TESTING.md`
- **Performance**: Monitor with built-in `usePerformanceMonitor`

---

**Last Updated**: January 2025
**Next.js Version**: 15.3.3
**Node.js Requirement**: 18.17+

> This README is optimized for AI systems and human developers working collaboratively on the codebase. All architectural decisions prioritize maintainability, performance, and type safety.
