import { NextRequest } from 'next/server'
import { generateText, UIMessage, convertToModelMessages } from 'ai'
import { azure } from '@ai-sdk/azure'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

interface ReasoningMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  parts: Array<{
    type: 'text'
    text: string
  }>
}

interface ReasoningResponse {
  id: string
  model: string
  created: number
  reasoning: {
    summary: string[]
    hasReasoning: boolean
  }
  message: {
    role: string
    content: string
  }
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    reasoning_tokens?: number
    total_tokens: number
  }
}

export async function POST(req: NextRequest) {
  try {
    const { messages }: { messages: ReasoningMessage[] } = await req.json()

    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 })
    }

    console.log(
      '🧠 [Reasoning API] Received request with',
      messages.length,
      'messages'
    )

    // Convert to UIMessage format for AI SDK
    const uiMessages: UIMessage[] = messages.map(msg => ({
      id: msg.id,
      role: msg.role,
      parts: msg.parts,
    }))

    // Try reasoning models first, then fall back to regular models
    const reasoningModels = ['o3-mini', 'o4-mini', 'o1-mini']
    const fallbackModels = ['gpt-4.1', 'gpt-4.1-mini']

    let response: any = null
    let modelUsed = ''
    let hasReasoning = false
    let reasoningSummary: string[] = []

    // Try reasoning models first
    for (const model of reasoningModels) {
      try {
        console.log(`🧠 [Reasoning API] Trying reasoning model: ${model}`)

        const result = await generateText({
          model: azure(model),
          messages: convertToModelMessages(uiMessages),
          temperature: 0.7,
          maxOutputTokens: 2000,
        })

        response = result
        modelUsed = model
        hasReasoning = true // Assume reasoning models provide reasoning
        reasoningSummary = [
          `Model ${model} used advanced reasoning to analyze this problem step by step.`,
        ]
        console.log(`🧠 [Reasoning API] Success with reasoning model: ${model}`)
        break
      } catch (error) {
        console.log(
          `🧠 [Reasoning API] Failed with ${model}:`,
          error instanceof Error ? error.message : 'Unknown error'
        )
        continue
      }
    }

    // If reasoning models failed, try fallback models
    if (!response) {
      for (const model of fallbackModels) {
        try {
          console.log(`🧠 [Reasoning API] Trying fallback model: ${model}`)

          const result = await generateText({
            model: azure(model),
            messages: convertToModelMessages(uiMessages),
            temperature: 0.7,
            maxOutputTokens: 2000,
          })

          response = result
          modelUsed = model
          hasReasoning = false
          console.log(
            `🧠 [Reasoning API] Success with fallback model: ${model}`
          )
          break
        } catch (error) {
          console.log(
            `🧠 [Reasoning API] Failed with ${model}:`,
            error instanceof Error ? error.message : 'Unknown error'
          )
          continue
        }
      }
    }

    // If all models failed
    if (!response) {
      return new Response(
        JSON.stringify({
          error: 'All models failed',
          details: 'Unable to generate response with any available model',
          modelsAttempted: [...reasoningModels, ...fallbackModels],
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }

    // Format response for frontend
    const formattedResponse: ReasoningResponse = {
      id: `reasoning-${Date.now()}`,
      model: modelUsed,
      created: Math.floor(Date.now() / 1000),
      reasoning: {
        summary: reasoningSummary,
        hasReasoning: hasReasoning,
      },
      message: {
        role: 'assistant',
        content: response.text,
      },
      usage: response.usage
        ? {
            prompt_tokens: response.usage.promptTokens,
            completion_tokens: response.usage.completionTokens,
            total_tokens: response.usage.totalTokens,
          }
        : undefined,
    }

    console.log('🧠 [Reasoning API] Response generated successfully')
    console.log('🧠 [Reasoning API] Model used:', modelUsed)
    console.log('🧠 [Reasoning API] Has reasoning:', hasReasoning)

    return new Response(JSON.stringify(formattedResponse), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('🧠 [Reasoning API] Unexpected error:', error)
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}
