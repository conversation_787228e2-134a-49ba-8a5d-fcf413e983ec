'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Brain, Zap, MessageSquare, AlertCircle } from 'lucide-react'

interface ReasoningResponse {
  id: string
  model: string
  created: number
  reasoning: {
    summary: string[]
    hasReasoning: boolean
  }
  message: {
    role: string
    content: string
  }
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    reasoning_tokens?: number
    total_tokens: number
  }
}

interface ErrorResponse {
  error: string
  details: string
  fallback?: string
}

export default function ReasoningDemoPage() {
  const [input, setInput] = useState<string>('')
  const [response, setResponse] = useState<ReasoningResponse | null>(null)
  const [error, setError] = useState<ErrorResponse | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const handleSubmit = async () => {
    if (!input.trim()) return

    setIsLoading(true)
    setError(null)
    setResponse(null)

    try {
      const res = await fetch('/api/chat-reasoning', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [
            {
              id: '1',
              role: 'user',
              parts: [
                {
                  type: 'text',
                  text: input,
                },
              ],
            },
          ],
        }),
      })

      const data = await res.json()

      if (!res.ok) {
        setError(data as ErrorResponse)
        return
      }

      setResponse(data as ReasoningResponse)
    } catch (err) {
      setError({
        error: 'Network error',
        details:
          err instanceof Error ? err.message : 'Failed to connect to API',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <div className="container mx-auto max-w-4xl p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Brain className="h-8 w-8 text-blue-600" />
          Azure OpenAI Reasoning Demo
        </h1>
        <p className="text-muted-foreground">
          Test Azure OpenAI's o-series models with reasoning summaries
        </p>
        <Badge variant="outline" className="text-xs">
          Uses Azure Responses API with o3-mini
        </Badge>
      </div>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Your Question
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            value={input}
            onChange={e => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask a complex question that requires reasoning... (e.g., 'Solve this step by step: If I have 3 boxes with 4 apples each, and I eat 2 apples from the first box, how many apples do I have left in total?')"
            className="min-h-[100px] resize-none"
            disabled={isLoading}
          />
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Press Cmd/Ctrl + Enter to submit
            </p>
            <Button
              onClick={handleSubmit}
              disabled={!input.trim() || isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Thinking...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4" />
                  Ask AI
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p className="font-medium text-red-800">{error.error}</p>
            <p className="text-sm text-red-600">{error.details}</p>
            {error.fallback && (
              <p className="text-sm text-blue-600 italic">{error.fallback}</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Response Display */}
      {response && (
        <div className="space-y-4">
          {/* Reasoning Summary */}
          {response.reasoning.hasReasoning && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <Brain className="h-5 w-5" />
                  AI Reasoning Process
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {response.reasoning.summary.map((step, index) => (
                    <div key={index} className="p-3 bg-white rounded-md border">
                      <p className="text-sm text-gray-700">{step}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Main Response */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                AI Response
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="whitespace-pre-wrap">
                  {response.message.content}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card className="bg-gray-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-sm">
                <Zap className="h-4 w-4" />
                Response Metadata
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="font-medium">Model</p>
                  <p className="text-muted-foreground">{response.model}</p>
                </div>
                <div>
                  <p className="font-medium">Response ID</p>
                  <p className="text-muted-foreground font-mono text-xs">
                    {response.id}
                  </p>
                </div>
                <div>
                  <p className="font-medium">Reasoning</p>
                  <Badge
                    variant={
                      response.reasoning.hasReasoning ? 'default' : 'secondary'
                    }
                  >
                    {response.reasoning.hasReasoning
                      ? 'Available'
                      : 'Not Available'}
                  </Badge>
                </div>
                <div>
                  <p className="font-medium">Created</p>
                  <p className="text-muted-foreground">
                    {new Date(response.created * 1000).toLocaleTimeString()}
                  </p>
                </div>
              </div>

              {response.usage && (
                <>
                  <Separator />
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="font-medium">Prompt Tokens</p>
                      <p className="text-muted-foreground">
                        {response.usage.prompt_tokens}
                      </p>
                    </div>
                    <div>
                      <p className="font-medium">Completion Tokens</p>
                      <p className="text-muted-foreground">
                        {response.usage.completion_tokens}
                      </p>
                    </div>
                    {response.usage.reasoning_tokens && (
                      <div>
                        <p className="font-medium">Reasoning Tokens</p>
                        <p className="text-muted-foreground text-blue-600 font-semibold">
                          {response.usage.reasoning_tokens}
                        </p>
                      </div>
                    )}
                    <div>
                      <p className="font-medium">Total Tokens</p>
                      <p className="text-muted-foreground">
                        {response.usage.total_tokens}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Instructions */}
      <Card className="bg-yellow-50 border-yellow-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-700 text-sm">
            <AlertCircle className="h-4 w-4" />
            Important Notes
          </CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-2 text-yellow-800">
          <ul className="list-disc list-inside space-y-1">
            <li>
              This demo requires access to Azure OpenAI o-series models
              (o3-mini, o4-mini)
            </li>
            <li>
              Reasoning summaries require special access - request at{' '}
              <a
                href="https://aka.ms/oai/o3access"
                className="underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                aka.ms/oai/o3access
              </a>
            </li>
            <li>
              The reasoning summary shows the AI's step-by-step thought process
            </li>
            <li>
              Try complex math problems, logic puzzles, or multi-step reasoning
              tasks
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
