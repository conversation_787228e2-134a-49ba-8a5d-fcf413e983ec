'use client'

import { useState } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'
import {
  PromptInput,
  PromptInputTextarea,
  PromptInputSubmit,
} from '@/components/ai-elements/prompt-input'

export default function ChatDemoPage() {
  // Local input with explicit type annotation for clarity
  const [input, setInput] = useState<string>('')

  const { messages, sendMessage, status, error } = useChat({
    transport: new DefaultChatTransport({ api: '/api/chat-demo' }),
    experimental_throttle: 200,
    onError: error => {
      console.error('Chat error:', error)
    },
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim() && status !== 'streaming') {
      sendMessage({ text: input })
      setInput('')
    }
  }

  return (
    <div className="flex h-screen flex-col bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card px-6 py-4">
        <h1 className="text-xl font-semibold text-foreground">
          AI SDK Elements Chat Demo
        </h1>
        <p className="text-sm text-muted-foreground">
          ChatGPT-style interface with tool calling and reasoning support
        </p>
      </div>

      {/* Chat Container */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <Conversation className="flex-1">
          <ConversationContent>
            {messages.length === 0 && (
              <div className="flex h-full items-center justify-center">
                <div className="text-center">
                  <h2 className="text-lg font-medium text-muted-foreground">
                    Start a conversation
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Try asking about the weather, or request a calculation
                  </p>
                </div>
              </div>
            )}

            {messages.map(message => (
              <Message key={message.id} from={message.role}>
                <MessageContent>
                  {/* Reasoning will be handled in message parts */}

                  {/* Render message parts */}
                  {message.parts.map((part, index) => {
                    if (part.type === 'text') {
                      return (
                        <Response
                          key={`${message.id}-text-${index}`}
                          defaultOrigin="https://localhost:3000"
                        >
                          {part.text}
                        </Response>
                      )
                    }

                    if (part.type === 'reasoning') {
                      return (
                        <Reasoning
                          key={`${message.id}-reasoning-${index}`}
                          isStreaming={
                            status === 'streaming' &&
                            message.id === messages[messages.length - 1]?.id
                          }
                          defaultOpen={false}
                        >
                          <ReasoningTrigger />
                          <ReasoningContent>{part.text}</ReasoningContent>
                        </Reasoning>
                      )
                    }

                    // Handle tool UI parts if present
                    const maybeTool = part as any
                    if (
                      maybeTool &&
                      typeof maybeTool === 'object' &&
                      'state' in maybeTool &&
                      'type' in maybeTool &&
                      (('input' in maybeTool &&
                        maybeTool.input !== undefined) ||
                        ('output' in maybeTool &&
                          maybeTool.output !== undefined) ||
                        ('errorText' in maybeTool && maybeTool.errorText))
                    ) {
                      return (
                        <Tool key={`${message.id}-tool-${index}`}>
                          <ToolHeader
                            type={maybeTool.type}
                            state={maybeTool.state}
                          />
                          <ToolContent>
                            {'input' in maybeTool && (
                              <ToolInput input={maybeTool.input} />
                            )}
                            <ToolOutput
                              output={
                                'output' in maybeTool ? (
                                  typeof maybeTool.output === 'string' ? (
                                    maybeTool.output
                                  ) : (
                                    <pre className="text-xs">
                                      {JSON.stringify(
                                        maybeTool.output,
                                        null,
                                        2
                                      )}
                                    </pre>
                                  )
                                ) : undefined
                              }
                              errorText={
                                'errorText' in maybeTool
                                  ? maybeTool.errorText
                                  : undefined
                              }
                            />
                          </ToolContent>
                        </Tool>
                      )
                    }

                    return null
                  })}
                </MessageContent>
              </Message>
            ))}

            {error && (
              <div className="mx-4 rounded-lg border border-destructive/20 bg-destructive/10 p-4">
                <p className="text-sm text-destructive">
                  Error: {error.message}
                </p>
              </div>
            )}
          </ConversationContent>
          <ConversationScrollButton />
        </Conversation>

        {/* Input Area */}
        <div className="border-t border-border bg-card p-4">
          <PromptInput onSubmit={handleSubmit} className="mx-auto max-w-4xl">
            <PromptInputTextarea
              value={input}
              onChange={e => setInput(e.target.value)}
              placeholder="Type your message here..."
              className="min-h-[60px] resize-none pr-12"
              disabled={status === 'streaming'}
            />
            <PromptInputSubmit
              status={status === 'streaming' ? 'streaming' : 'ready'}
              disabled={!input.trim() || status === 'streaming'}
              className="absolute bottom-2 right-2"
            />
          </PromptInput>
        </div>
      </div>
    </div>
  )
}
